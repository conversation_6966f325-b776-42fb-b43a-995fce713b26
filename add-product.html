<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>添加新产品 - 产品库存管理系统</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    .form-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    /* 标签页样式 */
    .tab-container {
      border-bottom: 1px solid #ddd;
    }

    .tab-nav {
      display: flex;
      background-color: #f8f9fa;
      border-radius: 8px 8px 0 0;
    }

    .tab-button {
      padding: 12px 20px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }

    .tab-button:hover {
      background-color: #e9ecef;
      color: #333;
    }

    .tab-button.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: white;
    }

    .tab-content {
      padding: 20px;
      min-height: 500px;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .form-group textarea {
      height: 80px;
      resize: vertical;
    }
    
    .form-row {
      display: flex;
      gap: 15px;
    }

    .form-row .form-group {
      flex: 1;
    }

    /* 三列布局的特殊样式 */
    .form-row.three-columns .form-group {
      flex: 1;
    }

    .form-row.three-columns .form-group:nth-child(2) {
      flex: 0 0 120px; /* 购买选项列固定宽度 */
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
      width: auto;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #007bff;
      margin: 25px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #007bff;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 30px;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }
    
    .btn-primary {
      background-color: #007bff;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #0056b3;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #545b62;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 20px;
      color: #007bff;
    }
    
    .error-message {
      color: #dc3545;
      font-size: 12px;
      margin-top: 5px;
      font-weight: bold;
      padding: 5px 8px;
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      display: none;
    }

    .error-message:not(:empty) {
      display: block;
    }
    
    .success-message {
      background-color: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      display: none;
    }

    /* 图片上传样式 */
    .image-upload-container {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }

    .image-upload-area {
      width: 400px;
      height: 400px;
      border: 2px dashed #ddd;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: border-color 0.3s ease;
      background-color: #fafafa;
      position: relative;
    }

    .image-upload-area:hover {
      border-color: #007bff;
    }

    .image-upload-area.dragover {
      border-color: #007bff;
      background-color: #f0f8ff;
    }

    .image-preview {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }

    .upload-placeholder {
      text-align: center;
      color: #666;
    }

    .upload-placeholder i {
      font-size: 48px;
      margin-bottom: 10px;
      color: #ccc;
    }

    .image-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      display: none;
    }

    .image-upload-area:hover .image-actions {
      display: block;
    }

    .image-actions button {
      background: rgba(0,0,0,0.7);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 8px;
      margin-left: 5px;
      cursor: pointer;
      font-size: 12px;
    }

    /* 用户组表格样式 */
    .user-group-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .user-group-table th,
    .user-group-table td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    .user-group-table th {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    .user-group-table input[type="checkbox"] {
      width: auto;
    }

    .user-group-table input[type="number"] {
      width: 80px;
      padding: 4px 8px;
    }

    /* 时间选择网格样式 */
    .time-grid {
      display: grid;
      grid-template-columns: 80px repeat(24, 1fr);
      gap: 1px;
      background-color: #ddd;
      border: 1px solid #ddd;
      margin: 15px 0;
    }

    .time-grid-header,
    .time-grid-cell {
      background-color: white;
      padding: 8px 4px;
      text-align: center;
      font-size: 12px;
    }

    .time-grid-header {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    .time-grid-day {
      background-color: #f8f9fa;
      font-weight: bold;
      display: flex;
      align-items: center;
      padding-left: 8px;
    }

    .time-slot {
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .time-slot:hover {
      background-color: #e3f2fd;
    }

    .time-slot.selected {
      background-color: #2196f3;
      color: white;
    }

    .date-range-container {
      display: flex;
      gap: 15px;
      align-items: center;
      margin: 15px 0;
    }

    /* 折叠功能样式 */
    .collapsible-section {
      margin: 20px 0;
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
    }

    .section-header {
      background-color: #f8f9fa;
      padding: 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
      user-select: none;
      transition: background-color 0.2s ease;
    }

    .section-header:hover {
      background-color: #e9ecef;
    }

    .section-header input[type="checkbox"] {
      width: auto;
      margin: 0;
    }

    .section-header .section-title {
      flex: 1;
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      color: #007bff;
      border: none;
      padding: 0;
    }

    .toggle-icon {
      font-size: 12px;
      transition: transform 0.2s ease;
    }

    .toggle-icon.expanded {
      transform: rotate(180deg);
    }

    .section-content {
      padding: 20px;
      border-top: 1px solid #ddd;
    }

    .section-content.collapsed {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>📦 添加新产品</h2>
    
    <div class="success-message" id="successMessage">
      产品添加成功！
    </div>
    
    <div class="form-container">
      <!-- 标签页导航 -->
      <div class="tab-container">
        <div class="tab-nav">
          <button type="button" class="tab-button active" data-tab="general">GENERAL</button>
          <button type="button" class="tab-button" data-tab="pricing">PRICING</button>
          <button type="button" class="tab-button" data-tab="restrictions">RESTRICTIONS</button>
          <button type="button" class="tab-button" data-tab="availability">AVAILABILITY</button>
          <button type="button" class="tab-button" data-tab="order">ORDER</button>
          <button type="button" class="tab-button" data-tab="stock">STOCK</button>
        </div>
      </div>

      <form id="addProductForm">
        <div class="tab-content">
          <!-- GENERAL 标签页 -->
          <div class="tab-pane active" id="general-tab">
            <div class="section-title">基本信息</div>

            <div class="image-upload-container">
              <div class="form-group" style="flex: 1;">
                <div class="form-row">
                  <div class="form-group">
                    <label for="name">产品名称 *</label>
                    <div style="display: flex; gap: 10px;">
                      <input type="text" id="name" name="name" required style="flex: 1;">
                      <button type="button" class="btn btn-secondary" onclick="checkAndSuggestName()" style="white-space: nowrap;">
                        检查可用性
                      </button>
                    </div>
                    <div class="error-message" id="nameError"></div>
                    <div id="nameAvailability" style="font-size: 12px; margin-top: 5px;"></div>
                  </div>

                  <div class="form-group">
                    <label for="productGroupId">产品组 *</label>
                    <select id="productGroupId" name="productGroupId" required>
                      <option value="">请选择产品组</option>
                    </select>
                    <div class="error-message" id="productGroupIdError"></div>
                  </div>
                </div>

                <div class="form-group">
                  <label for="description">产品描述</label>
                  <textarea id="description" name="description" placeholder="输入产品描述..."></textarea>
                </div>

                <!-- 价格和积分价格在一行，带购买选项 -->
                <div class="form-row three-columns">
                  <div class="form-group">
                    <label for="price">销售价格 (RM)</label>
                    <input type="number" id="price" name="price" step="0.01" min="0">
                  </div>

                  <div class="form-group">
                    <label for="purchaseOptions">购买选项</label>
                    <select id="purchaseOptions" name="purchaseOptions">
                      <option value="0">And</option>
                      <option value="1">Or</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label for="pointsPrice">积分价格</label>
                    <input type="number" id="pointsPrice" name="pointsPrice" step="0.01" min="0">
                  </div>
                </div>

                <!-- 成本和积分在一行 -->
                <div class="form-row">
                  <div class="form-group">
                    <label for="cost">成本价格 (RM)</label>
                    <input type="number" id="cost" name="cost" step="0.01" min="0">
                  </div>

                  <div class="form-group">
                    <label for="points">积分</label>
                    <input type="number" id="points" name="points" min="0">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="barcode">条码</label>
                    <input type="text" id="barcode" name="barcode">
                  </div>

                  <div class="form-group">
                    <label for="productType">产品类型</label>
                    <select id="productType" name="productType">
                      <option value="0">普通产品</option>
                      <option value="1">服务产品</option>
                    </select>
                  </div>
                </div>

              </div>

              <!-- 图片上传区域 -->
              <div class="form-group">
                <label>产品图片</label>
                <div class="image-upload-area" id="imageUploadArea">
                  <div class="upload-placeholder" id="uploadPlaceholder">
                    <div style="font-size: 48px; margin-bottom: 10px;">📷</div>
                    <div>400px × 400px</div>
                    <div style="margin-top: 10px; font-size: 12px;">点击或拖拽上传图片</div>
                  </div>
                  <img id="imagePreview" class="image-preview" style="display: none;">
                  <div class="image-actions">
                    <button type="button" id="deleteImageBtn" style="display: none;">删除</button>
                  </div>
                </div>
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
              </div>
            </div>
          </div>
          <!-- PRICING 标签页 -->
          <div class="tab-pane" id="pricing-tab">
            <div class="collapsible-section">
              <div class="section-header" onclick="toggleSection('pricing-content')">
                <input type="checkbox" id="enablePricing" onchange="toggleSectionByCheckbox('pricing-content', this)">
                <label for="enablePricing" class="section-title">启用用户组价格设置</label>
                <span class="toggle-icon">▼</span>
              </div>

              <div class="section-content" id="pricing-content" style="display: none;">
                <table class="user-group-table">
                  <thead>
                    <tr>
                      <th>启用</th>
                      <th>用户组</th>
                      <th>价格 (RM)</th>
                      <th>购买选项</th>
                      <th>积分价格</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="userPriceTableBody">
                    <!-- 动态加载用户组价格 -->
                  </tbody>
                </table>

                <button type="button" class="btn btn-secondary" id="addUserPriceBtn" style="margin-top: 15px;">
                  ➕ 添加用户组价格
                </button>
              </div>
            </div>
          </div>
        
          <!-- RESTRICTIONS 标签页 -->
          <div class="tab-pane" id="restrictions-tab">
            <div class="collapsible-section">
              <div class="section-header" onclick="toggleSection('restrictions-content')">
                <input type="checkbox" id="enableRestrictions" onchange="toggleSectionByCheckbox('restrictions-content', this)">
                <label for="enableRestrictions" class="section-title">启用用户组限制</label>
                <span class="toggle-icon">▼</span>
              </div>

              <div class="section-content" id="restrictions-content" style="display: none;">
                <table class="user-group-table">
                  <thead>
                    <tr>
                      <th>用户组名称</th>
                      <th>禁止</th>
                    </tr>
                  </thead>
                  <tbody id="disallowedGroupTableBody">
                    <!-- 动态加载用户组 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        
          <!-- AVAILABILITY 标签页 -->
          <div class="tab-pane" id="availability-tab">
            <div class="section-title">购买时间范围</div>

            <div class="section-title" style="font-size: 16px; color: #666;">时间范围</div>
            <div class="time-grid" id="timeGrid">
              <!-- 时间网格头部 -->
              <div class="time-grid-header"></div>
              <!-- 24小时列标题 -->
              <!-- 动态生成 -->
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="timeRange" name="timeRange">
                <label for="timeRange">启用时间范围限制</label>
              </div>
            </div>

            <div class="section-title" style="font-size: 16px; color: #666;">日期范围</div>
            <div class="date-range-container">
              <div class="form-group">
                <label for="startDate">开始日期</label>
                <input type="date" id="startDate" name="startDate">
              </div>

              <div class="form-group">
                <label for="endDate">结束日期</label>
                <input type="date" id="endDate" name="endDate">
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="dateRange" name="dateRange">
                <label for="dateRange">启用日期范围限制</label>
              </div>
            </div>
          </div>

          <!-- ORDER 标签页 -->
          <div class="tab-pane" id="order-tab">
            <div class="section-title">产品订单选项</div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="disallowClientOrder" name="disallowClientOrder">
                <label for="disallowClientOrder">禁止客户端订购</label>
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="restrictGuestSale" name="restrictGuestSale">
                <label for="restrictGuestSale">限制访客购买</label>
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="restrictSale" name="restrictSale">
                <label for="restrictSale">限制销售</label>
              </div>
            </div>

            <div class="form-group">
              <label for="displayOrder">显示顺序</label>
              <input type="number" id="displayOrder" name="displayOrder" min="0">
            </div>
          </div>

          <!-- STOCK 标签页 -->
          <div class="tab-pane" id="stock-tab">
            <div class="section-title">库存设置</div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="enableStock" name="enableStock" checked>
                <label for="enableStock">启用库存管理</label>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="stockAlertThreshold">库存警告阈值</label>
                <input type="number" id="stockAlertThreshold" name="stockAlertThreshold" min="0">
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="disallowSaleIfOutOfStock" name="disallowSaleIfOutOfStock">
                <label for="disallowSaleIfOutOfStock">缺货时禁止销售</label>
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <input type="checkbox" id="stockAlert" name="stockAlert">
                <label for="stockAlert">启用库存警告</label>
              </div>
            </div>
          </div>
        </div>

        <!-- 按钮组 -->
        <div class="button-group" style="padding: 0 20px 20px;">
          <a href="product.html" class="btn btn-secondary">返回产品列表</a>
          <button type="submit" class="btn btn-primary">添加产品</button>
        </div>
      </form>
    </div>
    
    <div class="loading" id="loadingIndicator">
      正在添加产品...
    </div>
  </div>

  <script src="js/api.js"></script>
  <script src="js/add-product.js"></script>
</body>
</html>
