<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>添加新产品 - 产品库存管理系统</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    .form-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .form-group textarea {
      height: 80px;
      resize: vertical;
    }
    
    .form-row {
      display: flex;
      gap: 15px;
    }
    
    .form-row .form-group {
      flex: 1;
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
      width: auto;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #007bff;
      margin: 25px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #007bff;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 30px;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }
    
    .btn-primary {
      background-color: #007bff;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #0056b3;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #545b62;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 20px;
      color: #007bff;
    }
    
    .error-message {
      color: #dc3545;
      font-size: 12px;
      margin-top: 5px;
    }
    
    .success-message {
      background-color: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>📦 添加新产品</h2>
    
    <div class="success-message" id="successMessage">
      产品添加成功！
    </div>
    
    <div class="form-container">
      <form id="addProductForm">
        <!-- 基本信息 -->
        <div class="section-title">基本信息</div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="name">产品名称 *</label>
            <input type="text" id="name" name="name" required>
            <div class="error-message" id="nameError"></div>
          </div>
          
          <div class="form-group">
            <label for="productGroupId">产品组 *</label>
            <select id="productGroupId" name="productGroupId" required>
              <option value="">请选择产品组</option>
            </select>
            <div class="error-message" id="productGroupIdError"></div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="description">产品描述</label>
          <textarea id="description" name="description" placeholder="输入产品描述..."></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="barcode">条码</label>
            <input type="text" id="barcode" name="barcode">
          </div>
          
          <div class="form-group">
            <label for="productType">产品类型</label>
            <select id="productType" name="productType">
              <option value="0">普通产品</option>
              <option value="1">服务产品</option>
            </select>
          </div>
        </div>
        
        <!-- 价格信息 -->
        <div class="section-title">价格信息</div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="price">销售价格 (RM)</label>
            <input type="number" id="price" name="price" step="0.01" min="0">
          </div>
          
          <div class="form-group">
            <label for="cost">成本价格 (RM)</label>
            <input type="number" id="cost" name="cost" step="0.01" min="0">
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="points">积分</label>
            <input type="number" id="points" name="points" min="0">
          </div>
          
          <div class="form-group">
            <label for="pointsPrice">积分价格</label>
            <input type="number" id="pointsPrice" name="pointsPrice" step="0.01" min="0">
          </div>
        </div>
        
        <!-- 库存设置 -->
        <div class="section-title">库存设置</div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="enableStock" name="enableStock" checked>
            <label for="enableStock">启用库存管理</label>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="stockAlertThreshold">库存警告阈值</label>
            <input type="number" id="stockAlertThreshold" name="stockAlertThreshold" min="0">
          </div>
          
          <div class="form-group">
            <label for="displayOrder">显示顺序</label>
            <input type="number" id="displayOrder" name="displayOrder" min="0">
          </div>
        </div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="disallowSaleIfOutOfStock" name="disallowSaleIfOutOfStock">
            <label for="disallowSaleIfOutOfStock">缺货时禁止销售</label>
          </div>
        </div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="stockAlert" name="stockAlert">
            <label for="stockAlert">启用库存警告</label>
          </div>
        </div>
        
        <!-- 销售限制 -->
        <div class="section-title">销售限制</div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="restrictSale" name="restrictSale">
            <label for="restrictSale">限制销售</label>
          </div>
        </div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="restrictGuestSale" name="restrictGuestSale">
            <label for="restrictGuestSale">限制访客购买</label>
          </div>
        </div>
        
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="disallowClientOrder" name="disallowClientOrder">
            <label for="disallowClientOrder">禁止客户端订购</label>
          </div>
        </div>
        
        <!-- 按钮组 -->
        <div class="button-group">
          <a href="product.html" class="btn btn-secondary">返回产品列表</a>
          <button type="submit" class="btn btn-primary">添加产品</button>
        </div>
      </form>
    </div>
    
    <div class="loading" id="loadingIndicator">
      正在添加产品...
    </div>
  </div>

  <script src="js/api.js"></script>
  <script src="js/add-product.js"></script>
</body>
</html>
