/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h2, h3 {
    color: #343a40;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

button {
    width: 100%;
    background-color: #007bff;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

.error {
    color: #dc3545;
    margin-top: 10px;
    font-weight: bold;
}

/* 产品页面样式 */
#searchInput {
    margin-bottom: 15px;
    padding: 10px;
    width: 100%;
    max-width: 400px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.group-button {
    padding: 15px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-size: 14px;
}

.group-button:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-button[data-expanded="true"] {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.product-list {
    margin-top: 20px;
}

.product-item {
    background-color: #ffffff;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: box-shadow 0.3s ease;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.stock-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stock-input {
    width: 80px;
    padding: 5px;
}

.add-stock-btn, .remove-stock-btn, .set-stock-btn {
    padding: 5px 10px;
    width: auto;
    font-size: 12px;
}

.add-stock-btn {
    background-color: #28a745; /* 绿色 */
}

.add-stock-btn:hover {
    background-color: #218838;
}

.remove-stock-btn {
    background-color: #dc3545; /* 红色 */
}

.remove-stock-btn:hover {
    background-color: #c82333;
}

.set-stock-btn {
    background-color: #ffc107; /* 黄色 */
    color: #212529;
}

.set-stock-btn:hover {
    background-color: #e0a800;
}

.product-info {
    background-color: #f8f9fa;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.product-info div {
    margin-bottom: 5px;
}

#logoutBtn {
    width: auto;
    background-color: #dc3545;
    padding: 10px 20px;
    margin-top: 20px;
}

#logoutBtn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    font-size: 18px;
    color: #6c757d;
    margin: 20px 0;
}

.edit-btn, .edit-form button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 10px;
    width: auto;
}

.edit-form {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px;
    align-items: center;
}

.edit-form label {
    font-weight: bold;
    text-align: right;
}

.edit-form input {
    margin: 0;
    padding: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.edit-form button {
    grid-column: 1 / -1; /* 让按钮横跨两列 */
}

/* 库存显示样式 */
.stock-display {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
    transition: all 0.3s ease;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.stock-normal {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    color: #2d5a2d;
    border: 1px solid #c3e6c3;
    box-shadow: 0 2px 4px rgba(45, 90, 45, 0.1);
}

.stock-zero {
    background: linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%);
    color: #d32f2f;
    border: 1px solid #ffcdd2;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 库存控制区域样式优化 */
.stock-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.stock-control .stock-input {
    width: 60px;
    padding: 4px 6px;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.stock-control button {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0;
    width: auto;
    min-width: 50px;
}

/* 编辑按钮容器样式 */
.edit-button-container {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.edit-btn.quick-edit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.edit-btn.quick-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.edit-btn.advanced-edit {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.edit-btn.advanced-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* 高级编辑表单样式 */
.advanced-edit-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
    border: 1px solid #e9ecef;
}

.advanced-edit-form h4 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 18px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.edit-section {
    margin-bottom: 25px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.edit-section h5 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.edit-section .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.edit-section .form-group {
    flex: 1;
}

.edit-section .form-group.full-width {
    flex: 1 1 100%;
}

.advanced-edit-form .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.advanced-edit-form .form-group {
    flex: 1;
}

.advanced-edit-form .form-group.full-width {
    flex: 1 1 100%;
}

.edit-section .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

.edit-section .form-group input,
.edit-section .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.edit-section .form-group input:focus,
.edit-section .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.edit-section .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.edit-section .checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.edit-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.edit-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-buttons button:first-child {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.edit-buttons button:first-child:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.edit-buttons button:last-child {
    background: #6c757d;
    color: white;
}

.edit-buttons button:last-child:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* 高级编辑手风琴样式 */
.advanced-edit-form .accordion-item {
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.advanced-edit-form .accordion-header {
    background: #f8f9fa;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}

.advanced-edit-form .accordion-header:hover {
    background: #e9ecef;
}

.advanced-edit-form .accordion-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.advanced-edit-form .accordion-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.advanced-edit-form .accordion-icon.rotated {
    transform: rotate(180deg);
}

.advanced-edit-form .accordion-content {
    padding: 0 15px;
    background: white;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), padding 0.4s ease;
}

.advanced-edit-form .accordion-content.expanded {
    max-height: 1000px;
    padding: 15px;
}

/* 图片上传区域样式 */
.image-upload-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.image-placeholder {
    width: 100px;
    height: 100px;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 12px;
}

/* 用户价格表格样式 */
.user-price-table,
.restricted-groups-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.user-price-table th,
.user-price-table td,
.restricted-groups-table th,
.restricted-groups-table td {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    text-align: left;
}

.user-price-table th,
.restricted-groups-table th {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 13px;
    color: #495057;
}

.user-price-table input,
.user-price-table select,
.restricted-groups-table input,
.restricted-groups-table select {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
}

.user-price-table button,
.restricted-groups-table button {
    padding: 4px 8px;
    font-size: 11px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: #dc3545;
    color: white;
}

.user-price-table button:hover,
.restricted-groups-table button:hover {
    background: #c82333;
}

/* 选择框样式 */
.advanced-edit-form select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.advanced-edit-form select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 状态指示器样式 */
.status-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-indicator.allowed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-indicator.restricted {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 错误消息样式 */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin: 10px 0;
    font-size: 14px;
}

/* 优化复选框和标签样式 */
.restricted-groups-table input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.restricted-groups-table label {
    font-weight: 500;
    cursor: pointer;
}