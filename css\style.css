/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h2, h3 {
    color: #343a40;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

button {
    width: 100%;
    background-color: #007bff;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

.error {
    color: #dc3545;
    margin-top: 10px;
    font-weight: bold;
}

/* 产品页面样式 */
#searchInput {
    margin-bottom: 15px;
    padding: 10px;
    width: 100%;
    max-width: 400px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.group-button {
    padding: 15px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-size: 14px;
}

.group-button:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-button[data-expanded="true"] {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.product-list {
    margin-top: 20px;
}

.product-item {
    background-color: #ffffff;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: box-shadow 0.3s ease;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.stock-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stock-input {
    width: 80px;
    padding: 5px;
}

.add-stock-btn, .remove-stock-btn, .set-stock-btn {
    padding: 5px 10px;
    width: auto;
    font-size: 12px;
}

.add-stock-btn {
    background-color: #28a745; /* 绿色 */
}

.add-stock-btn:hover {
    background-color: #218838;
}

.remove-stock-btn {
    background-color: #dc3545; /* 红色 */
}

.remove-stock-btn:hover {
    background-color: #c82333;
}

.set-stock-btn {
    background-color: #ffc107; /* 黄色 */
    color: #212529;
}

.set-stock-btn:hover {
    background-color: #e0a800;
}

.product-info {
    background-color: #f8f9fa;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.product-info div {
    margin-bottom: 5px;
}

#logoutBtn {
    width: auto;
    background-color: #dc3545;
    padding: 10px 20px;
    margin-top: 20px;
}

#logoutBtn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    font-size: 18px;
    color: #6c757d;
    margin: 20px 0;
}

.edit-btn, .edit-form button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 10px;
    width: auto;
}

.edit-form {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px;
    align-items: center;
}

.edit-form label {
    font-weight: bold;
    text-align: right;
}

.edit-form input {
    margin: 0;
    padding: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.edit-form button {
    grid-column: 1 / -1; /* 让按钮横跨两列 */
}

/* 库存显示样式 */
.stock-display {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
    transition: all 0.3s ease;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.stock-normal {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    color: #2d5a2d;
    border: 1px solid #c3e6c3;
    box-shadow: 0 2px 4px rgba(45, 90, 45, 0.1);
}

.stock-zero {
    background: linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%);
    color: #d32f2f;
    border: 1px solid #ffcdd2;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 库存控制区域样式优化 */
.stock-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.stock-control .stock-input {
    width: 60px;
    padding: 4px 6px;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.stock-control button {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0;
    width: auto;
    min-width: 50px;
}

/* 编辑按钮容器样式 */
.edit-button-container {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.edit-btn.quick-edit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.edit-btn.quick-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.edit-btn.advanced-edit {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.edit-btn.advanced-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* 高级编辑表单样式 */
.advanced-edit-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
    border: 1px solid #e9ecef;
}

.advanced-edit-form h4 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 18px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.edit-section {
    margin-bottom: 25px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.edit-section h5 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.edit-section .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.edit-section .form-group {
    flex: 1;
}

.edit-section .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

.edit-section .form-group input,
.edit-section .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.edit-section .form-group input:focus,
.edit-section .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.edit-section .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.edit-section .checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.edit-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.edit-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-buttons button:first-child {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.edit-buttons button:first-child:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.edit-buttons button:last-child {
    background: #6c757d;
    color: white;
}

.edit-buttons button:last-child:hover {
    background: #545b62;
    transform: translateY(-1px);
}