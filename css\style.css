/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h2, h3 {
    color: #343a40;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

button {
    width: 100%;
    background-color: #007bff;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

.error {
    color: #dc3545;
    margin-top: 10px;
    font-weight: bold;
}

/* 产品页面样式 */
#searchInput {
    margin-bottom: 15px;
    padding: 10px;
    width: 100%;
    max-width: 400px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.group-button {
    padding: 15px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-size: 14px;
}

.group-button:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-button[data-expanded="true"] {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.product-list {
    margin-top: 20px;
}

.product-item {
    background-color: #ffffff;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: box-shadow 0.3s ease;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.stock-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stock-input {
    width: 80px;
    padding: 5px;
}

.add-stock-btn, .remove-stock-btn, .set-stock-btn {
    padding: 5px 10px;
    width: auto;
    font-size: 12px;
}

.add-stock-btn {
    background-color: #28a745; /* 绿色 */
}

.add-stock-btn:hover {
    background-color: #218838;
}

.remove-stock-btn {
    background-color: #dc3545; /* 红色 */
}

.remove-stock-btn:hover {
    background-color: #c82333;
}

.set-stock-btn {
    background-color: #ffc107; /* 黄色 */
    color: #212529;
}

.set-stock-btn:hover {
    background-color: #e0a800;
}

.product-info {
    background-color: #f8f9fa;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.product-info div {
    margin-bottom: 5px;
}

#logoutBtn {
    width: auto;
    background-color: #dc3545;
    padding: 10px 20px;
    margin-top: 20px;
}

#logoutBtn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    font-size: 18px;
    color: #6c757d;
    margin: 20px 0;
}

.edit-btn, .edit-form button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 10px;
    width: auto;
}

.edit-form {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px;
    align-items: center;
}

.edit-form label {
    font-weight: bold;
    text-align: right;
}

.edit-form input {
    margin: 0;
    padding: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.edit-form button {
    grid-column: 1 / -1; /* 让按钮横跨两列 */
}