// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登入');
        window.location.href = 'login.html';
        return;
    }

    console.log('页面初始化开始');

    try {
        await loadProductGroups();
        console.log('产品组加载完成');

        await checkExistingProduct(); // 检查现有产品结构
        console.log('现有产品检查完成');

        setupFormHandlers();
        console.log('表单处理器设置完成');

        console.log('页面初始化完成');
    } catch (error) {
        console.error('页面初始化失败:', error);
        alert('页面初始化失败，请刷新页面重试');
    }
});

// 检查现有产品的数据结构
async function checkExistingProduct() {
    try {
        // 使用正确的API函数获取产品列表
        const data = await apiRequest('/products?Pagination.Limit=1');
        const products = data.result?.data || data.result || [];
        if (products.length > 0) {
            console.log('现有产品数据结构示例:', products[0]);
        } else {
            console.log('没有找到现有产品');
        }
    } catch (error) {
        console.error('获取现有产品失败:', error);
        // 不阻止页面加载，只是记录错误
    }
}

// 加载产品组列表
async function loadProductGroups() {
    try {
        const data = await fetchProductGroups();
        const groups = data.result?.data || data.result || [];
        const select = document.getElementById('productGroupId');
        
        // 清空现有选项（保留默认选项）
        select.innerHTML = '<option value="">请选择产品组</option>';
        
        // 添加产品组选项
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            select.appendChild(option);
        });
        
        console.log('产品组加载完成:', groups);
    } catch (error) {
        console.error('加载产品组失败:', error);
        alert('加载产品组失败，请刷新页面重试。');
    }
}

// 设置表单处理器
function setupFormHandlers() {
    const form = document.getElementById('addProductForm');
    form.addEventListener('submit', handleFormSubmit);
    
    // 启用库存管理复选框变化时的处理
    const enableStockCheckbox = document.getElementById('enableStock');
    enableStockCheckbox.addEventListener('change', toggleStockFields);
    
    // 初始化库存字段状态
    toggleStockFields();
    
    console.log('表单处理器设置完成');
}

// 切换库存相关字段的启用状态
function toggleStockFields() {
    const enableStock = document.getElementById('enableStock').checked;
    const stockFields = [
        'stockAlertThreshold',
        'disallowSaleIfOutOfStock',
        'stockAlert'
    ];
    
    stockFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field.type === 'checkbox') {
            field.disabled = !enableStock;
            if (!enableStock) {
                field.checked = false;
            }
        } else {
            field.disabled = !enableStock;
            if (!enableStock) {
                field.value = '';
            }
        }
    });
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();
    
    console.log('表单提交开始');
    
    // 清除之前的错误信息
    clearErrorMessages();
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 显示加载指示器
    const loadingIndicator = document.getElementById('loadingIndicator');
    const submitButton = document.querySelector('button[type="submit"]');
    
    loadingIndicator.style.display = 'block';
    submitButton.disabled = true;
    
    try {
        // 构建产品数据
        const productData = buildProductData();
        console.log('发送的产品数据:', productData);
        
        // 调用API创建产品
        const result = await createProduct(productData);
        console.log('API返回结果:', result);
        
        // 显示成功消息
        showSuccessMessage();
        
        // 重置表单
        document.getElementById('addProductForm').reset();
        
        // 重新初始化库存字段状态
        document.getElementById('enableStock').checked = true;
        toggleStockFields();
        
    } catch (error) {
        console.error('创建产品失败:', error);
        alert('创建产品失败: ' + error.message);
    } finally {
        loadingIndicator.style.display = 'none';
        submitButton.disabled = false;
    }
}

// 验证表单
function validateForm() {
    let isValid = true;
    
    // 验证产品名称
    const name = document.getElementById('name').value.trim();
    if (!name) {
        showFieldError('name', '产品名称不能为空');
        isValid = false;
    }
    
    // 验证产品组
    const productGroupId = document.getElementById('productGroupId').value;
    if (!productGroupId) {
        showFieldError('productGroupId', '请选择产品组');
        isValid = false;
    }
    
    // 验证价格（如果填写了）
    const price = document.getElementById('price').value;
    if (price && parseFloat(price) < 0) {
        showFieldError('price', '价格不能为负数');
        isValid = false;
    }
    
    // 验证成本（如果填写了）
    const cost = document.getElementById('cost').value;
    if (cost && parseFloat(cost) < 0) {
        showFieldError('cost', '成本不能为负数');
        isValid = false;
    }
    
    console.log('表单验证结果:', isValid);
    return isValid;
}

// 构建产品数据 - 基于现有产品结构
function buildProductData() {
    const formData = new FormData(document.getElementById('addProductForm'));

    const productGroupId = parseInt(formData.get('productGroupId'));
    const name = formData.get('name')?.trim();

    if (!productGroupId || !name) {
        throw new Error('产品组ID和产品名称是必需的');
    }

    // 基于现有产品的结构，包含基本必需字段
    const productData = {
        productType: parseInt(formData.get('productType')) || 0,
        productGroupId: productGroupId,
        name: name,
        description: formData.get('description')?.trim() || "",
        price: formData.get('price') ? parseFloat(formData.get('price')) : 0,
        cost: formData.get('cost') ? parseFloat(formData.get('cost')) : 0,
        disallowClientOrder: formData.get('disallowClientOrder') === 'on',
        restrictGuestSale: formData.get('restrictGuestSale') === 'on',
        restrictSale: formData.get('restrictSale') === 'on',
        purchaseOptions: 0,
        points: formData.get('points') ? parseInt(formData.get('points')) : 0,
        pointsPrice: formData.get('pointsPrice') ? parseFloat(formData.get('pointsPrice')) : 0,
        barcode: formData.get('barcode')?.trim() || "",
        enableStock: formData.get('enableStock') === 'on',
        disallowSaleIfOutOfStock: formData.get('disallowSaleIfOutOfStock') === 'on',
        stockAlert: formData.get('stockAlert') === 'on',
        stockAlertThreshold: formData.get('stockAlertThreshold') ? parseInt(formData.get('stockAlertThreshold')) : 0,
        stockTargetDifferentProduct: false,
        stockTargetProductId: 0,
        stockProductAmount: 0,
        isDeleted: false,
        isService: (parseInt(formData.get('productType')) || 0) === 1,
        displayOrder: formData.get('displayOrder') ? parseInt(formData.get('displayOrder')) : 0
    };

    return productData;
}

// 构建稍微完整的产品数据
function buildMinimalProductData() {
    const formData = new FormData(document.getElementById('addProductForm'));

    const productGroupId = parseInt(formData.get('productGroupId'));
    const name = formData.get('name')?.trim();

    if (!productGroupId || !name) {
        throw new Error('产品组ID和产品名称是必需的');
    }

    return {
        productType: 0,
        productGroupId: productGroupId,
        name: name,
        description: "",
        price: 0,
        cost: 0,
        disallowClientOrder: false,
        restrictGuestSale: false,
        restrictSale: false,
        purchaseOptions: 0,
        points: 0,
        pointsPrice: 0,
        barcode: "",
        enableStock: true,
        disallowSaleIfOutOfStock: false,
        stockAlert: false,
        stockAlertThreshold: 0,
        stockTargetDifferentProduct: false,
        stockTargetProductId: 0,
        stockProductAmount: 0,
        isDeleted: false,
        isService: false,
        displayOrder: 0
    };
}

// 构建完整产品数据（备用）
function buildFullProductData() {
    const formData = new FormData(document.getElementById('addProductForm'));

    const productGroupId = parseInt(formData.get('productGroupId'));
    const name = formData.get('name')?.trim();

    if (!productGroupId || !name) {
        throw new Error('产品组ID和产品名称是必需的');
    }

    return {
        productType: parseInt(formData.get('productType')) || 0,
        productGroupId: productGroupId,
        name: name,
        description: formData.get('description')?.trim() || "",
        price: formData.get('price') ? parseFloat(formData.get('price')) : 0,
        cost: formData.get('cost') ? parseFloat(formData.get('cost')) : 0,
        disallowClientOrder: formData.get('disallowClientOrder') === 'on',
        restrictGuestSale: formData.get('restrictGuestSale') === 'on',
        restrictSale: formData.get('restrictSale') === 'on',
        purchaseOptions: 0,
        points: formData.get('points') ? parseInt(formData.get('points')) : 0,
        pointsPrice: formData.get('pointsPrice') ? parseFloat(formData.get('pointsPrice')) : 0,
        barcode: formData.get('barcode')?.trim() || "",
        enableStock: formData.get('enableStock') === 'on',
        disallowSaleIfOutOfStock: formData.get('disallowSaleIfOutOfStock') === 'on',
        stockAlert: formData.get('stockAlert') === 'on',
        stockAlertThreshold: formData.get('stockAlertThreshold') ? parseInt(formData.get('stockAlertThreshold')) : 0,
        stockTargetDifferentProduct: false,
        stockTargetProductId: 0,
        stockProductAmount: 0,
        isDeleted: false,
        isService: (parseInt(formData.get('productType')) || 0) === 1,
        displayOrder: formData.get('displayOrder') ? parseInt(formData.get('displayOrder')) : 0,
        timeProduct: {
            minutes: 0,
            expiresAtLogout: false,
            expireAtDayTime: false,
            expireAtDayTimeMinute: 0,
            expireAfterTime: false,
            expireAfterType: 0,
            expiresAfter: 0,
            expiresFrom: 0,
            useOrder: 0
        },
        bundle: {
            selfStock: true
        }
    };
}

// 显示字段错误信息
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(fieldId + 'Error');
    if (errorElement) {
        errorElement.textContent = message;
    }
}

// 清除错误信息
function clearErrorMessages() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// 显示成功消息
function showSuccessMessage() {
    const successMessage = document.getElementById('successMessage');
    successMessage.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        successMessage.style.display = 'none';
    }, 3000);
    
    // 滚动到顶部显示成功消息
    window.scrollTo({ top: 0, behavior: 'smooth' });
}
