async function login() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    const errorMsg = document.getElementById('error');
    errorMsg.textContent = '';

    if (!username || !password) {
        errorMsg.textContent = '请输入用户名和密码';
        return;
    }

    try {
        const data = await loginUser(username, password);
        const token = data?.result?.token;
        if (token) {
            localStorage.setItem('token', 'Bearer ' + token);
            window.location.href = 'product.html';
        } else {
            errorMsg.textContent = '登入失败：无有效 token';
        }
    } catch (err) {
        errorMsg.textContent = '网络错误：' + err.message;
    }
}