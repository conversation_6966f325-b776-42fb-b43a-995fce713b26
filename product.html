<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>产品库存查询系统</title>
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
    <h3>📁 PRODUCT GROUP（点击展开加载产品）</h3>
    <a href="add-product.html" class="btn" style="background-color: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">➕ 添加产品</a>
  </div>
  <input type="text" id="searchInput" placeholder="🔍 搜索产品组、产品或条码..." />
  <div id="loadingIndicator" class="loading" style="display: none;">正在加载数据...</div>
  <div id="groupGrid" class="grid-container"></div>
  <div id="productList" class="product-list"></div>
  <button id="logoutBtn">🚪 登出</button>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
</body>
</html>
