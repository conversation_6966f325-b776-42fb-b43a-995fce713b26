<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>产品库存查询系统</title>
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
    <h3>📁 PRODUCT GROUP（点击展开加载产品）</h3>
    <a href="add-product.html" class="btn" style="background-color: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">➕ 添加产品</a>
  </div>
  <input type="text" id="searchInput" placeholder="🔍 搜索产品组、产品或条码..." />

  <!-- 排序选项 -->
  <div class="sort-container" style="margin: 15px 0; display: flex; gap: 15px; align-items: center;">
    <label for="sortBy" style="font-weight: bold; color: #333;">排序方式：</label>
    <select id="sortBy" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
      <option value="">默认排序</option>
      <option value="name_asc">产品名称 A-Z</option>
      <option value="name_desc">产品名称 Z-A</option>
      <option value="stock_desc">库存最多到最少</option>
      <option value="stock_asc">库存最少到最多</option>
      <option value="price_desc">价格高到低</option>
      <option value="price_asc">价格低到高</option>
      <option value="id_desc">最新添加</option>
      <option value="id_asc">最早添加</option>
    </select>

    <button id="applySortBtn" class="btn" style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
      应用排序
    </button>
  </div>

  <div id="loadingIndicator" class="loading" style="display: none;">正在加载数据...</div>
  <div id="groupGrid" class="grid-container"></div>
  <div id="productList" class="product-list"></div>
  <button id="logoutBtn">🚪 登出</button>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
</body>
</html>
