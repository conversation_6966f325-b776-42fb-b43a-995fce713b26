### 库存系统管理网页


### api token 

eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6IiIsInJvbGUiOiJvcGVyYXRvciIsIm5hbWUiOiIxIiwibmFtZWlkIjoiMjI0MyIsIkFwcHMiOiIqIiwiRGVwbG95bWVudCI6IioiLCJMb2ciOlsiKiIsIkNsZWFyIl0sIk1hbmFnZW1lbnQiOlsiKiIsIkZpbGVzIiwiTG9ja1N0YXRlIiwiTWFpbnRlbmFuY2UiLCJNb2R1bGVSZXN0YXJ0IiwiUG93ZXJPbkVuZHBvaW50cyIsIlByb2Nlc3NlcyIsIlNlY3VyaXR5IiwiVGFza3MiXSwiTW9uaXRvcmluZyI6IioiLCJOZXdzIjoiKiIsIlJlZ2lzdGVyVHJhbnNhY3Rpb25zIjpbIlJlZ2lzdGVyVHJhbnNhY3Rpb25zUGF5SW4iLCJSZWdpc3RlclRyYW5zYWN0aW9uc1BheU91dCJdLCJSZXBvcnRzIjoiKiIsIlNhbGUiOlsiKiIsIkFsbG93RGlzYWJsZVJlY2VpcHRQcmludCIsIkFsbG93VGltZUNyZWRpdCIsIkN1c3RvbVByaWNlIiwiRGVsZXRlVGltZVB1cmNoYXNlcyIsIkRlcG9zaXQiLCJNYW51YWxPcGVuQ2FzaERyYXdlciIsIk1vZGlmeUJpbGxpbmdPcHRpb25zIiwiUGF5TGF0ZXIiLCJWaWV3RGVwb3NpdHMiLCJWaWV3SW52b2ljZXMiLCJWaWV3UGFpZEludm9pY2VzIiwiVmlld1Bhc3REYXlzRGVwb3NpdHMiLCJWaWV3UGFzdERheXNJbnZvaWNlcyIsIlZpZXdQYXN0RGF5c1JlZ2lzdGVyVHJhbnNhY3Rpb25zIiwiVmlld1JlZ2lzdGVyVHJhbnNhY3Rpb25zIiwiVm9pZENsb3NlZFNoaWZ0SW52b2ljZXMiLCJWb2lkRGVwb3NpdHMiLCJWb2lkSW52b2ljZXMiLCJWb2lkT3RoZXJPcGVyYXRvckludm9pY2VzIiwiVm9pZFBhc3REYXlzSW52b2ljZXMiLCJWb2lkVXNlZFRpbWVJbnZvaWNlcyIsIldpdGhkcmF3Il0sIlNldHRpbmdzIjoiKiIsIlNoaWZ0IjoiVmlld0V4cGVjdGVkIiwiU3RvY2siOlsiKiIsIk1hbmFnZSIsIlZpZXdQYXN0RGF5c1N0b2NrVHJhbnNhY3Rpb25zIiwiVmlld1N0b2NrVHJhbnNhY3Rpb25zIl0sIlVzZXIiOlsiQWNjZXNzU3RhdHMiLCJBZGQiLCJDaGFuZ2VVc2VyR3JvdXAiLCJDaGFuZ2VVc2VyTmFtZSIsIkRlbGV0ZSIsIkVkaXQiLCJVc2VyRGlzYWJsZSIsIlVzZXJFbmFibGUiLCJVc2VyTWFudWFsTG9naW4iLCJVc2VyUGFzc3dvcmRSZXNldCJdLCJXYWl0aW5nTGluZXMiOlsiKiIsIk1hbmFnZSJdLCJXZWJBcGkiOiIqIiwibmJmIjoxNzUyMzc4NzQyLCJleHAiOjE3NTI0NjUxNDIsImlhdCI6MTc1MjM3ODc0Mn0._6p8csmSd7PX-qQjvGichzH3VMAei_Iku7xSedxcy2Y


### ProductGroups
*   **GET /api/v2.0/productgroups**: Get all product groups.
*   **POST /api/v2.0/productgroups**: Create a product group.
*   **PUT /api/v2.0/productgroups**: Update a product group.
*   **GET /api/v2.0/productgroups/{id}**: Get a product group by id.
*   **DELETE /api/v2.0/productgroups/{id}**: Delete a product group.

### Products
*   **GET /api/v2.0/products**: Get all products.
*   **POST /api/v2.0/products**: Create a product.
*   **PUT /api/v2.0/products**: Update a product.
*   **GET /api/v2.0/products/{id}**: Get a product by id.

### ProductsStock
*   **GET /api/v2.0/productsstock**: Get all products stock.

### Invoices
*   **GET /api/v2.0/invoices**: Get all invoices.
*   **GET /api/v2.0/invoices/{id}**: Get an invoice by id.
*   **PUT /api/v2.0/invoices/{id}/void**: Void the specified invoice.

### StockTransactions
*   **GET /api/v2.0/stocktransactions**: Get all stock transactions.
*   **GET /api/v2.0/stocktransactions/{id}**: Get a stock transaction by id.