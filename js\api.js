// API 配置
const API_BASE_URL = 'http://gizmodelphine.ddns.net:8081/api/v2.0';

// 获取存储的 Token 并返回认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    if (!token) {
        // 如果没有令牌，则重定向到登录页面
        window.location.href = 'login.html';
        throw new Error('未找到认证令牌，请先登录。');
    }
    return {
        'accept': 'application/json',
        'Authorization': token
    };
}

// 通用的、需要认证的 API 请求函数
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const fetchOptions = {
        ...options,
        headers: {
            ...getAuthHeaders(),
            ...options.headers,
        }
    };

    try {
        const response = await fetch(url, fetchOptions);
        if (!response.ok) {
            // 如果令牌失效 (401), 清除并重定向
            if (response.status === 401) {
                localStorage.removeItem('token');
                window.location.href = 'login.html';
            }

            // 尝试获取错误详情
            let errorMessage = `HTTP 错误! 状态: ${response.status}`;
            try {
                const errorData = await response.json();
                console.error('API错误详情:', errorData);
                errorMessage += ` - ${JSON.stringify(errorData)}`;
            } catch (e) {
                // 如果无法解析错误响应，使用默认消息
            }

            throw new Error(errorMessage);
        }
        return await response.json();
    } catch (error) {
        console.error(`API 请求失败: ${url}`, error);
        throw error;
    }
}

// 获取产品组
async function fetchProductGroups() {
    return apiRequest('/productgroups?Pagination.Limit=1000');
}

// 获取所有产品
async function fetchAllProducts() {
    return apiRequest('/products?Pagination.Limit=10000');
}

// 获取单个产品库存
async function fetchProductStock(productId) {
    try {
        const data = await apiRequest(`/productsstock/${productId}`);
        return data.result?.onHand ?? '未知';
    } catch (error) {
        console.error(`获取产品 ${productId} 库存失败:`, error);
        return '未知';
    }
}

// 登录函数 (这是一个公共请求，不需要认证)
async function loginUser(username, password) {
    const endpoint = `/auth/accesstoken?Username=${encodeURIComponent(username)}&Password=${encodeURIComponent(password)}`;
    const url = `${API_BASE_URL}${endpoint}`;
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP 错误! 状态: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('登录 API 请求失败:', error);
        throw error;
    }
}

// 更新产品信息 (PUT 请求)
async function updateProduct(productData) {
    return apiRequest(`/products`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
    });
}

// 更新产品库存 (PUT 请求)
async function updateProductStock(productId, amount, type) {
    const payload = {
        id: productId,
        amount: amount
    };

    // 只有在 type 是 0 或 1 时才添加 type 字段
    if (type === 0 || type === 1) {
        payload.type = type;
    }

    return apiRequest(`/productsstock`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
    });
}

// 创建产品 (POST 请求)
async function createProduct(productData) {
    return apiRequest(`/products`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
    });
}

// 上传产品图片 (POST 请求)
async function uploadProductImage(productId, imageBase64) {
    return apiRequest(`/products/${productId}/images`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            image: imageBase64
        })
    });
}

// 删除产品图片 (DELETE 请求)
async function deleteProductImage(productId, productImageId) {
    return apiRequest(`/products/${productId}/images/${productImageId}`, {
        method: 'DELETE'
    });
}

// 获取用户组列表
async function fetchUserGroups() {
    return apiRequest('/usergroups?Pagination.Limit=1000');
}

// 创建产品用户价格 (POST 请求)
async function createProductUserPrice(productId, userPriceData) {
    return apiRequest(`/products/${productId}/userprices`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userPriceData)
    });
}

// 更新产品用户价格 (PUT 请求)
async function updateProductUserPrice(userPriceData) {
    return apiRequest(`/products/userprices`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userPriceData)
    });
}

// 删除产品用户价格 (DELETE 请求)
async function deleteProductUserPrice(productId, userPriceId) {
    return apiRequest(`/products/${productId}/userprices/${userPriceId}`, {
        method: 'DELETE'
    });
}

// 创建禁止用户组 (POST 请求)
async function createDisallowedUserGroup(productId, disallowedGroupData) {
    return apiRequest(`/products/${productId}/disallowedusergroups`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(disallowedGroupData)
    });
}

// 更新禁止用户组 (PUT 请求)
async function updateDisallowedUserGroup(disallowedGroupData) {
    return apiRequest(`/products/disallowedusergroups`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(disallowedGroupData)
    });
}

// 更新产品购买可用性 (PUT 请求)
async function updateProductPurchaseAvailability(productId, availabilityData) {
    return apiRequest(`/products/${productId}/purchaseavailability`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(availabilityData)
    });
}

// 更新产品 (PUT 请求)
async function updateProduct(productData) {
    return apiRequest(`/products`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
    });
}

// 更新产品图片 (PUT 请求)
async function updateProductImage(imageData) {
    return apiRequest(`/products/images`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(imageData)
    });
}

// 更新产品用户价格 (PUT 请求) - 已存在，但确保正确
async function updateProductUserPrice(userPriceData) {
    return apiRequest(`/products/userprices`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userPriceData)
    });
}

// 更新禁止用户组 (PUT 请求) - 已存在，但确保正确
async function updateDisallowedUserGroup(disallowedGroupData) {
    return apiRequest(`/products/disallowedusergroups`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(disallowedGroupData)
    });
}