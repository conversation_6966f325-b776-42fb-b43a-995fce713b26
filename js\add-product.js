// 全局变量
let currentProductId = null;
let userGroups = [];
let userPrices = [];
let disallowedGroups = [];

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登入');
        window.location.href = 'login.html';
        return;
    }

    await loadInitialData();
    setupFormHandlers();
    setupTabHandlers();
    setupImageUpload();
    setupTimeGrid();

    // 恢复之前保存的表单状态
    restoreFormState();
});

// 加载初始数据
async function loadInitialData() {
    try {
        const [productGroupsData, userGroupsData] = await Promise.all([
            fetchProductGroups(),
            fetchUserGroups()
        ]);

        // 加载产品组
        const groups = productGroupsData.result?.data || productGroupsData.result || [];
        const select = document.getElementById('productGroupId');
        select.innerHTML = '<option value="">请选择产品组</option>';
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            select.appendChild(option);
        });

        // 保存用户组数据
        userGroups = userGroupsData.result?.data || userGroupsData.result || [];

        // 初始化用户组相关表格
        initializeUserPriceTable();
        initializeDisallowedGroupTable();

    } catch (error) {
        console.error('加载初始数据失败:', error);
        alert('加载数据失败，请刷新页面重试。');
    }
}

// 设置表单处理器
function setupFormHandlers() {
    const form = document.getElementById('addProductForm');
    form.addEventListener('submit', handleFormSubmit);

    // 启用库存管理复选框变化时的处理
    const enableStockCheckbox = document.getElementById('enableStock');
    enableStockCheckbox.addEventListener('change', toggleStockFields);

    // 添加用户价格按钮
    document.getElementById('addUserPriceBtn').addEventListener('click', addUserPriceRow);

    // 初始化库存字段状态
    toggleStockFields();
}

// 设置标签页处理器
function setupTabHandlers() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // 激活当前标签
            button.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

// 设置图片上传
function setupImageUpload() {
    const uploadArea = document.getElementById('imageUploadArea');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const deleteBtn = document.getElementById('deleteImageBtn');

    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        if (!imagePreview.style.display || imagePreview.style.display === 'none') {
            imageInput.click();
        }
    });

    // 文件选择
    imageInput.addEventListener('change', handleImageSelect);

    // 拖拽上传
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageFile(files[0]);
        }
    });

    // 删除图片
    deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        clearImagePreview();
    });
}

// 切换库存相关字段的启用状态
function toggleStockFields() {
    const enableStock = document.getElementById('enableStock').checked;
    const stockFields = [
        'stockAlertThreshold',
        'disallowSaleIfOutOfStock',
        'stockAlert'
    ];
    
    stockFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field.type === 'checkbox') {
            field.disabled = !enableStock;
            if (!enableStock) {
                field.checked = false;
            }
        } else {
            field.disabled = !enableStock;
            if (!enableStock) {
                field.value = '';
            }
        }
    });
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();

    // 清除之前的错误信息
    clearErrorMessages();

    // 验证表单
    if (!validateForm()) {
        return;
    }

    // 显示加载指示器
    const loadingIndicator = document.getElementById('loadingIndicator');
    const submitButton = document.querySelector('button[type="submit"]');

    loadingIndicator.style.display = 'block';
    submitButton.disabled = true;

    try {
        // 使用修复后的产品数据构建函数
        const productData = buildProductData();
        console.log('发送的产品数据:', JSON.stringify(productData, null, 2));

        // 调用API创建产品
        const result = await createProduct(productData);
        console.log('API返回结果:', result);
        currentProductId = result.result?.id || result.id;

        // 处理图片上传
        await handleImageUpload();

        // 处理用户价格设置
        await handleUserPricesSubmit();

        // 处理禁止用户组设置
        await handleDisallowedGroupsSubmit();

        // 保存当前表单状态
        saveFormState();

        // 显示成功消息
        showSuccessMessage();

        // 只清空产品名称，保留其他设置
        document.getElementById('name').value = '';

        clearImagePreview();

        // 重新初始化表格（但保持用户的选择）
        // initializeUserPriceTable();
        // initializeDisallowedGroupTable();

    } catch (error) {
        console.error('创建产品失败:', error);

        // 处理特定的错误类型
        if (error.message.includes('Name value') && error.message.includes('is not unique')) {
            // 产品名称重复错误
            showFieldError('name', '❌ 产品名称已存在，请使用不同的名称');
            document.getElementById('name').focus();
            document.getElementById('name').select();
        } else if (error.message.includes('Barcode') && error.message.includes('is not unique')) {
            // 条码重复错误
            showFieldError('barcode', '❌ 条码已存在，请使用不同的条码');
            document.getElementById('barcode').focus();
            document.getElementById('barcode').select();
        } else if (error.message.includes('ProductGroupId') && error.message.includes('not found')) {
            // 产品组不存在错误
            showFieldError('productGroupId', '❌ 选择的产品组无效，请重新选择');
        } else if (error.message.includes('400')) {
            // 其他400验证错误
            alert('❌ 数据验证失败: 请检查输入的数据格式是否正确');
        } else {
            // 其他错误
            alert('❌ 创建产品失败: ' + error.message);
        }
    } finally {
        loadingIndicator.style.display = 'none';
        submitButton.disabled = false;
    }
}

// 验证表单
function validateForm() {
    let isValid = true;
    
    // 验证产品名称
    const name = document.getElementById('name').value.trim();
    if (!name) {
        showFieldError('name', '产品名称不能为空');
        isValid = false;
    }
    
    // 验证产品组
    const productGroupId = document.getElementById('productGroupId').value;
    if (!productGroupId) {
        showFieldError('productGroupId', '请选择产品组');
        isValid = false;
    }
    
    // 验证价格（如果填写了）
    const price = document.getElementById('price').value;
    if (price && parseFloat(price) < 0) {
        showFieldError('price', '价格不能为负数');
        isValid = false;
    }
    
    // 验证成本（如果填写了）
    const cost = document.getElementById('cost').value;
    if (cost && parseFloat(cost) < 0) {
        showFieldError('cost', '成本不能为负数');
        isValid = false;
    }
    
    return isValid;
}

// 构建产品数据 - 使用经过测试的工作版本
function buildProductData() {
    const formData = new FormData(document.getElementById('addProductForm'));

    // 获取表单值，确保类型正确
    const productGroupId = parseInt(formData.get('productGroupId'));
    const name = formData.get('name')?.trim();

    // 验证必需字段
    if (!productGroupId || !name) {
        throw new Error('产品组ID和产品名称是必需的');
    }

    // 回到之前成功的基本版本
    const productData = {
        productGroupId: productGroupId,
        name: name,
        description: formData.get('description')?.trim() || "",
        price: formData.get('price') ? parseFloat(formData.get('price')) : 0,
        cost: formData.get('cost') ? parseFloat(formData.get('cost')) : 0,
        isDeleted: false  // 关键字段！确保产品在系统中显示
    };

    // 只有在有值时才添加可选字段
    const productType = parseInt(formData.get('productType'));
    if (productType) {
        productData.productType = productType;
        productData.isService = productType === 1;
    }

    const points = parseInt(formData.get('points'));
    if (points) {
        productData.points = points;
    }

    const pointsPrice = parseFloat(formData.get('pointsPrice'));
    if (pointsPrice) {
        productData.pointsPrice = pointsPrice;
    }

    const barcode = formData.get('barcode')?.trim();
    if (barcode) {
        productData.barcode = barcode;
    }

    const displayOrder = parseInt(formData.get('displayOrder'));
    if (displayOrder) {
        productData.displayOrder = displayOrder;
    }

    // 购买选项
    const purchaseOptions = parseInt(formData.get('purchaseOptions'));
    if (purchaseOptions !== undefined && purchaseOptions !== null) {
        productData.purchaseOptions = purchaseOptions;
    }

    // 布尔字段
    if (formData.get('enableStock') === 'on') {
        productData.enableStock = true;
    }

    if (formData.get('disallowClientOrder') === 'on') {
        productData.disallowClientOrder = true;
    }

    if (formData.get('restrictGuestSale') === 'on') {
        productData.restrictGuestSale = true;
    }

    if (formData.get('restrictSale') === 'on') {
        productData.restrictSale = true;
    }

    if (formData.get('stockAlert') === 'on') {
        productData.stockAlert = true;
        const threshold = parseInt(formData.get('stockAlertThreshold'));
        if (threshold) {
            productData.stockAlertThreshold = threshold;
        }
    }

    if (formData.get('disallowSaleIfOutOfStock') === 'on') {
        productData.disallowSaleIfOutOfStock = true;
    }

    return productData;
}

// 构建最简单的产品数据（用于测试）
function buildMinimalProductData() {
    const formData = new FormData(document.getElementById('addProductForm'));

    const productGroupId = parseInt(formData.get('productGroupId'));
    const name = formData.get('name')?.trim();

    if (!productGroupId || !name) {
        throw new Error('产品组ID和产品名称是必需的');
    }

    return {
        productType: 0,
        productGroupId: productGroupId,
        name: name,
        description: "",
        price: 0,
        cost: 0,
        disallowClientOrder: false,
        restrictGuestSale: false,
        restrictSale: false,
        purchaseOptions: 0,
        points: 0,
        pointsPrice: 0,
        barcode: "",
        enableStock: true,
        disallowSaleIfOutOfStock: false,
        stockAlert: false,
        stockAlertThreshold: 0,
        stockTargetDifferentProduct: false,
        stockTargetProductId: 0,
        stockProductAmount: 0,
        isDeleted: false,
        isService: false,
        displayOrder: 0,
        timeProduct: {
            minutes: 0,
            expiresAtLogout: false,
            expireAtDayTime: false,
            expireAtDayTimeMinute: 0,
            expireAfterTime: false,
            expireAfterType: 0,
            expiresAfter: 0,
            expiresFrom: 0,
            useOrder: 0
        },
        bundle: {
            selfStock: true
        }
    };
}

// 显示字段错误信息
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(fieldId + 'Error');
    if (errorElement) {
        errorElement.textContent = message;
    }
}

// 清除错误信息
function clearErrorMessages() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// 显示成功消息
function showSuccessMessage() {
    const successMessage = document.getElementById('successMessage');
    successMessage.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        successMessage.style.display = 'none';
    }, 3000);

    // 滚动到顶部显示成功消息
    window.scrollTo({ top: 0, behavior: 'smooth' });
}



// 处理图片选择
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleImageFile(file);
    }
}

// 处理图片文件
function handleImageFile(file) {
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB限制
        alert('图片文件大小不能超过5MB');
        return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
        showImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

// 显示图片预览
function showImagePreview(imageSrc) {
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const deleteBtn = document.getElementById('deleteImageBtn');

    imagePreview.src = imageSrc;
    imagePreview.style.display = 'block';
    uploadPlaceholder.style.display = 'none';
    deleteBtn.style.display = 'inline-block';
}

// 清除图片预览
function clearImagePreview() {
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const deleteBtn = document.getElementById('deleteImageBtn');
    const imageInput = document.getElementById('imageInput');

    imagePreview.style.display = 'none';
    imagePreview.src = '';
    uploadPlaceholder.style.display = 'block';
    deleteBtn.style.display = 'none';
    imageInput.value = '';
}

// 设置时间网格
function setupTimeGrid() {
    const timeGrid = document.getElementById('timeGrid');
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    // 清空网格
    timeGrid.innerHTML = '';

    // 添加空的左上角单元格
    const emptyCell = document.createElement('div');
    emptyCell.className = 'time-grid-header';
    timeGrid.appendChild(emptyCell);

    // 添加小时标题
    for (let hour = 0; hour < 24; hour++) {
        const hourCell = document.createElement('div');
        hourCell.className = 'time-grid-header';
        hourCell.textContent = hour.toString().padStart(2, '0');
        timeGrid.appendChild(hourCell);
    }

    // 添加每天的时间槽
    days.forEach(day => {
        // 添加日期标签
        const dayCell = document.createElement('div');
        dayCell.className = 'time-grid-day';
        dayCell.textContent = day;
        timeGrid.appendChild(dayCell);

        // 添加24个小时的时间槽
        for (let hour = 0; hour < 24; hour++) {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-grid-cell time-slot';
            timeSlot.dataset.day = day;
            timeSlot.dataset.hour = hour;

            timeSlot.addEventListener('click', () => {
                timeSlot.classList.toggle('selected');
            });

            timeGrid.appendChild(timeSlot);
        }
    });
}

// 初始化用户价格表格
function initializeUserPriceTable() {
    const tbody = document.getElementById('userPriceTableBody');
    tbody.innerHTML = '';

    userGroups.forEach(group => {
        const row = createUserPriceRow(group);
        tbody.appendChild(row);
    });
}

// 创建用户价格行
function createUserPriceRow(userGroup) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="checkbox" data-user-group-id="${userGroup.id}">
        </td>
        <td>${userGroup.name}</td>
        <td>
            <input type="number" step="0.01" min="0" placeholder="0.00" data-field="price">
        </td>
        <td>
            <select data-field="purchaseOptions">
                <option value="0">And</option>
                <option value="1">Or</option>
            </select>
        </td>
        <td>
            <input type="number" step="0.01" min="0" placeholder="0.00" data-field="pointsPrice">
        </td>
        <td>
            <button type="button" class="btn btn-secondary" onclick="removeUserPriceRow(this)">删除</button>
        </td>
    `;
    return row;
}

// 添加用户价格行
function addUserPriceRow() {
    // 这里可以添加选择用户组的逻辑
    alert('请在现有用户组中启用价格设置');
}

// 删除用户价格行
function removeUserPriceRow(button) {
    const row = button.closest('tr');
    const checkbox = row.querySelector('input[type="checkbox"]');
    checkbox.checked = false;

    // 清空价格输入
    const priceInputs = row.querySelectorAll('input[type="number"]');
    priceInputs.forEach(input => input.value = '');
}

// 初始化禁止用户组表格
function initializeDisallowedGroupTable() {
    const tbody = document.getElementById('disallowedGroupTableBody');
    tbody.innerHTML = '';

    userGroups.forEach(group => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${group.name}</td>
            <td>
                <input type="checkbox" data-user-group-id="${group.id}">
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 折叠功能
function toggleSection(contentId) {
    const content = document.getElementById(contentId);
    const icon = event.target.closest('.section-header').querySelector('.toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.classList.remove('expanded');
    }
}

function toggleSectionByCheckbox(contentId, checkbox) {
    const content = document.getElementById(contentId);
    const icon = checkbox.closest('.section-header').querySelector('.toggle-icon');

    if (checkbox.checked) {
        content.style.display = 'block';
        icon.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.classList.remove('expanded');
    }
}

// 保存表单状态
function saveFormState() {
    const formData = new FormData(document.getElementById('addProductForm'));
    const state = {};

    // 保存所有表单字段
    for (let [key, value] of formData.entries()) {
        state[key] = value;
    }

    // 保存复选框状态
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        state[checkbox.id || checkbox.name] = checkbox.checked;
    });

    // 保存折叠状态
    const collapsibleSections = ['enablePricing', 'enableRestrictions'];
    collapsibleSections.forEach(sectionId => {
        const checkbox = document.getElementById(sectionId);
        if (checkbox) {
            state[sectionId] = checkbox.checked;
        }
    });

    localStorage.setItem('productFormState', JSON.stringify(state));
}

// 恢复表单状态
function restoreFormState() {
    const savedState = localStorage.getItem('productFormState');
    if (!savedState) return;

    try {
        const state = JSON.parse(savedState);

        // 恢复表单字段
        Object.keys(state).forEach(key => {
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = state[key];
                    // 如果是折叠控制复选框，触发折叠功能
                    if (key === 'enablePricing' && state[key]) {
                        toggleSectionByCheckbox('pricing-content', element);
                    } else if (key === 'enableRestrictions' && state[key]) {
                        toggleSectionByCheckbox('restrictions-content', element);
                    }
                } else {
                    element.value = state[key];
                }
            }
        });
    } catch (error) {
        console.error('恢复表单状态失败:', error);
    }
}

// 处理图片上传
async function handleImageUpload() {
    const imagePreview = document.getElementById('imagePreview');
    if (!currentProductId || !imagePreview.src || imagePreview.style.display === 'none') {
        return;
    }

    try {
        // 将图片转换为base64（去掉data:image/...;base64,前缀）
        const base64Data = imagePreview.src.split(',')[1];
        await uploadProductImage(currentProductId, base64Data);
    } catch (error) {
        console.error('上传图片失败:', error);
        // 不阻止产品创建，只是警告
        alert('产品创建成功，但图片上传失败: ' + error.message);
    }
}

// 处理用户价格提交
async function handleUserPricesSubmit() {
    if (!currentProductId) return;

    const userPriceRows = document.querySelectorAll('#userPriceTableBody tr');

    for (const row of userPriceRows) {
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (!checkbox.checked) continue;

        const userGroupId = parseInt(checkbox.dataset.userGroupId);
        const priceInput = row.querySelector('input[data-field="price"]');
        const pointsPriceInput = row.querySelector('input[data-field="pointsPrice"]');
        const purchaseOptionsSelect = row.querySelector('select[data-field="purchaseOptions"]');

        const price = parseFloat(priceInput.value) || 0;
        const pointsPrice = parseFloat(pointsPriceInput.value) || 0;
        const purchaseOptions = parseInt(purchaseOptionsSelect.value) || 0;

        if (price > 0 || pointsPrice > 0) {
            try {
                await createProductUserPrice(currentProductId, {
                    userGroupId: userGroupId,
                    price: price,
                    pointsPrice: pointsPrice,
                    purchaseOptions: purchaseOptions,
                    isEnabled: true
                });
            } catch (error) {
                console.error('创建用户价格失败:', error);
                alert(`为用户组设置价格失败: ${error.message}`);
            }
        }
    }
}

// 处理禁止用户组提交
async function handleDisallowedGroupsSubmit() {
    if (!currentProductId) return;

    const disallowedRows = document.querySelectorAll('#disallowedGroupTableBody tr');

    for (const row of disallowedRows) {
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (!checkbox.checked) continue;

        const userGroupId = parseInt(checkbox.dataset.userGroupId);

        try {
            await createDisallowedUserGroup(currentProductId, {
                userGroupId: userGroupId,
                isDisallowed: true
            });
        } catch (error) {
            console.error('创建禁止用户组失败:', error);
            alert(`设置禁止用户组失败: ${error.message}`);
        }
    }
}
