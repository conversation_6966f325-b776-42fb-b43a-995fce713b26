// 全局变量
let allGroups = [];
let allProducts = [];
const productCache = {}; // 缓存产品列表
let searchTimeout = null; // 搜索防抖定时器

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登入');
        window.location.href = 'login.html';
        return;
    }

    document.getElementById('logoutBtn').onclick = handleLogout;
    document.getElementById('searchInput').addEventListener('input', handleSearchWithDelay);
    document.getElementById('applySortBtn').addEventListener('click', handleSort);
    // 移除自动排序，只有点击按钮才排序

    await loadInitialData();
});

// 登出处理
function handleLogout() {
    localStorage.removeItem('token');
    window.location.href = 'login.html';
}

// 加载初始数据
async function loadInitialData() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = 'block';

    try {
        const [groupsData, productsData] = await Promise.all([
            fetchProductGroups(),
            fetchAllProducts()
        ]);

        allGroups = groupsData.result?.data || groupsData.result || [];
        allProducts = productsData.result?.data || [];

        renderGroups(allGroups);
    } catch (error) {
        console.error('加载初始数据失败:', error);
        alert('加载数据失败，请稍后重试。');
    } finally {
        loadingIndicator.style.display = 'none';
    }
}

// 渲染产品组
function renderGroups(groups) {
    const container = document.getElementById('groupGrid');
    container.innerHTML = '';
    groups.forEach(group => {
        const btn = document.createElement('div');
        btn.className = 'group-button';
        btn.textContent = group.name;
        btn.onclick = () => toggleProducts(group.id, btn);
        container.appendChild(btn);
    });
}

// 切换产品显示
async function toggleProducts(groupId, btnElement) {
    const isExpanded = btnElement.getAttribute('data-expanded') === 'true';
    const listContainer = document.getElementById('productList');

    // 重置所有按钮状态
    document.querySelectorAll('.group-button').forEach(btn => btn.setAttribute('data-expanded', 'false'));
    
    if (isExpanded) {
        listContainer.innerHTML = '';
        return;
    }

    btnElement.setAttribute('data-expanded', 'true');
    
    let products = productCache[groupId];
    if (!products) {
        products = allProducts.filter(p => p.productGroupId === groupId);
        productCache[groupId] = products;
    }
    renderProducts(products);
}

// 渲染产品列表
async function renderProducts(products) {
    const container = document.getElementById('productList');
    container.innerHTML = '';
    for (const product of products) {
        const item = document.createElement('div');
        item.className = 'product-item';
        item.dataset.productId = product.id;

        const stock = await fetchProductStock(product.id);

        // 根据库存数量设置样式
        const stockClass = stock === 0 ? 'stock-zero' : 'stock-normal';

        item.innerHTML = `
            <span class="product-name">📦 ${product.name}</span>
            <div class="stock-control">
                <span class="stock-display ${stockClass}">库存: ${stock}</span>
                <input type="number" class="stock-input" placeholder="数量">
                <button class="add-stock-btn">增加</button>
                <button class="remove-stock-btn">移除</button>
            </div>
        `;

        const stockInput = item.querySelector('.stock-input');
        const stockSpan = item.querySelector('.stock-control span');

        item.querySelector('.add-stock-btn').addEventListener('click', async (event) => {
            event.stopPropagation();
            const amount = parseInt(stockInput.value, 10);
            if (!isNaN(amount)) {
                await updateStock(product.id, amount, 0); // type 0 = Add
                const currentStock = parseInt(stockSpan.textContent.split(': ')[1], 10);
                const newStock = currentStock + amount;
                stockSpan.textContent = `库存: ${newStock}`;

                // 更新库存样式
                updateStockStyle(stockSpan, newStock);

                stockInput.value = '';
            }
        });

        item.querySelector('.remove-stock-btn').addEventListener('click', async (event) => {
            event.stopPropagation();
            const amount = parseInt(stockInput.value, 10);
            if (!isNaN(amount)) {
                await updateStock(product.id, amount, 1); // type 1 = Remove
                const currentStock = parseInt(stockSpan.textContent.split(': ')[1], 10);
                const newStock = Math.max(0, currentStock - amount);
                stockSpan.textContent = `库存: ${newStock}`;

                // 更新库存样式
                updateStockStyle(stockSpan, newStock);

                stockInput.value = '';
            }
        });

        item.querySelector('.product-name').addEventListener('click', () => {
            toggleProductDetails(product, item);
        });

        container.appendChild(item);
    }
}

// 更新库存
async function updateStock(productId, amount, type) {
    try {
        await updateProductStock(productId, amount, type);
    } catch (error) {
        alert('更新库存失败: ' + error.message);
    }
}

// 切换产品详情
async function toggleProductDetails(product, itemElement) {
    const existingInfo = itemElement.querySelector('.product-info');
    if (existingInfo) {
        existingInfo.remove();
        return;
    }

    const stock = await fetchProductStock(product.id);
    const infoContainer = document.createElement('div');
    infoContainer.className = 'product-info';

    // 设置产品信息
    infoContainer.innerHTML = `
        <div><strong>${product.name}</strong></div>
        <div>价格：RM ${product.price?.toFixed(2) || 'N/A'}</div>
        <div>成本：RM ${product.cost?.toFixed(2) || 'N/A'}</div>
        <div>库存：${stock}</div>
        <div>条码：${product.barcode || '无'}</div>
    `;

    // 创建并添加“修改”按钮
    const editButton = document.createElement('button');
    editButton.className = 'edit-btn';
    editButton.textContent = '修改';
    editButton.addEventListener('click', (event) => {
        event.stopPropagation();
        showEditForm(infoContainer, product); // 正确传递 product 对象
    });

    infoContainer.appendChild(editButton);
    itemElement.appendChild(infoContainer);
}

// 显示产品编辑表单
function showEditForm(infoContainer, product) {
    infoContainer.innerHTML = `
        <div class="edit-form">
            <label>Name:</label>
            <input type="text" id="edit-name-${product.id}" value="${product.name}">
            <label>Price:</label>
            <input type="number" id="edit-price-${product.id}" value="${product.price || 0}">
            <label>Cost:</label>
            <input type="number" id="edit-cost-${product.id}" value="${product.cost || 0}">
            <label>Barcode:</label>
            <input type="text" id="edit-barcode-${product.id}" value="${product.barcode || ''}">
            <button id="save-btn-${product.id}">保存</button>
            <button id="cancel-btn-${product.id}">取消</button>
        </div>
    `;

    // 阻止整个表单的点击事件冒泡
    infoContainer.querySelector('.edit-form').addEventListener('click', (event) => {
        event.stopPropagation();
    });

    // 为按钮添加事件监听器
    document.getElementById(`save-btn-${product.id}`).addEventListener('click', () => saveProductChanges(product.id));
    document.getElementById(`cancel-btn-${product.id}`).addEventListener('click', () => cancelEdit(infoContainer));
}

// 取消编辑
function cancelEdit(infoContainer) {
    const productId = infoContainer.parentElement.dataset.productId;
    const product = allProducts.find(p => p.id == productId);
    toggleProductDetails(product, infoContainer.parentElement);
}

// 保存产品更改
async function saveProductChanges(productId) {
    const product = allProducts.find(p => p.id == productId);
    const updatedProduct = {
        ...product,
        name: document.getElementById(`edit-name-${productId}`).value,
        price: parseFloat(document.getElementById(`edit-price-${productId}`).value),
        cost: parseFloat(document.getElementById(`edit-cost-${productId}`).value),
        barcode: document.getElementById(`edit-barcode-${productId}`).value,
    };

    try {
        await updateProduct(updatedProduct);
        // 更新本地缓存
        const index = allProducts.findIndex(p => p.id == productId);
        allProducts[index] = updatedProduct;
        // 重新渲染产品详情
        const itemElement = document.querySelector(`[data-product-id='${productId}']`);
        const infoContainer = itemElement.querySelector('.product-info');
        infoContainer.remove(); // 先移除旧的详情
        toggleProductDetails(updatedProduct, itemElement); // 重新创建并显示新的详情
    } catch (error) {
        alert('更新产品失败: ' + error.message);
    }
}

// 带防抖的搜索处理
function handleSearchWithDelay() {
    // 清除之前的定时器
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // 设置新的定时器，300ms后执行搜索
    searchTimeout = setTimeout(() => {
        handleSearch();
    }, 300);
}

// 处理搜索
function handleSearch() {
    const keyword = document.getElementById('searchInput').value.toLowerCase().trim();

    if (!keyword) {
        renderGroups(allGroups);
        document.getElementById('productList').innerHTML = '';
        return;
    }

    // 1. 过滤产品 - 根据产品名称和条码匹配
    const matchedProducts = allProducts.filter(product => {
        const productName = product.name.toLowerCase();
        const barcode = (product.barcode || '').toLowerCase();

        // 产品名称包含关键词或条码以关键词开头
        return productName.includes(keyword) || barcode.startsWith(keyword);
    });

    // 2. 分别处理产品组匹配和产品匹配
    const groupNameMatchedGroups = allGroups.filter(group => {
        const groupName = group.name.toLowerCase();
        return groupName.includes(keyword);
    });

    const productMatchedGroupIds = new Set(matchedProducts.map(p => p.productGroupId));
    const productMatchedGroups = allGroups.filter(group =>
        productMatchedGroupIds.has(group.id) && !groupNameMatchedGroups.some(g => g.id === group.id)
    );

    // 3. 合并所有匹配的产品组
    const allMatchedGroups = [...groupNameMatchedGroups, ...productMatchedGroups];

    // 4. 确定要显示的产品
    let finalProducts = [];

    // 对于产品组名称匹配的组，显示该组的所有产品
    for (const group of groupNameMatchedGroups) {
        const groupProducts = allProducts.filter(p => p.productGroupId === group.id);
        finalProducts.push(...groupProducts);
    }

    // 对于因包含匹配产品而被选中的组，只显示匹配的产品
    for (const group of productMatchedGroups) {
        const groupMatchedProducts = matchedProducts.filter(p => p.productGroupId === group.id);
        finalProducts.push(...groupMatchedProducts);
    }

    // 5. 渲染结果
    renderGroups(allMatchedGroups);
    if (finalProducts.length > 0) {
        renderProducts(finalProducts);
    } else {
        document.getElementById('productList').innerHTML = '<p>未找到匹配的产品。</p>';
    }
}

// 排序处理 - 简化版本
function handleSort() {
    const sortBy = document.getElementById('sortBy').value;
    const productList = document.getElementById('productList');

    console.log('开始排序，排序方式:', sortBy);

    if (!sortBy) {
        console.log('没有选择排序方式');
        return;
    }

    // 获取所有产品元素
    const productElements = Array.from(document.querySelectorAll('.product-item'));

    if (productElements.length === 0) {
        console.log('没有产品可排序');
        return;
    }

    // 显示加载状态
    const sortBtn = document.getElementById('applySortBtn');
    const originalText = sortBtn.textContent;
    sortBtn.textContent = '排序中...';
    sortBtn.disabled = true;

    try {
        // 为每个元素提取排序数据
        const elementsWithData = productElements.map(element => {
            const productId = parseInt(element.dataset.productId);
            const nameElement = element.querySelector('.product-name');
            const stockElement = element.querySelector('.stock-control span');

            const name = nameElement ? nameElement.textContent.replace('📦 ', '') : '';
            const stockText = stockElement ? stockElement.textContent : '';
            const stockMatch = stockText.match(/库存:\s*(\d+)/);
            const stock = stockMatch ? parseInt(stockMatch[1]) : 0;

            // 从allProducts中获取价格信息
            const productData = allProducts.find(p => p.id === productId);
            const price = productData ? (parseFloat(productData.price) || 0) : 0;

            return {
                element,
                productId,
                name,
                stock,
                price
            };
        });

        console.log('提取的数据示例:', elementsWithData.slice(0, 3).map(item => ({
            id: item.productId,
            name: item.name,
            stock: item.stock,
            price: item.price
        })));

        // 排序
        elementsWithData.sort((a, b) => {
            switch (sortBy) {
                case 'name_asc':
                    return a.name.localeCompare(b.name, 'zh-CN', { numeric: true });
                case 'name_desc':
                    return b.name.localeCompare(a.name, 'zh-CN', { numeric: true });
                case 'stock_desc':
                    return b.stock - a.stock;
                case 'stock_asc':
                    return a.stock - b.stock;
                case 'price_desc':
                    return b.price - a.price;
                case 'price_asc':
                    return a.price - b.price;
                case 'id_desc':
                    return b.productId - a.productId;
                case 'id_asc':
                    return a.productId - b.productId;
                default:
                    return 0;
            }
        });

        console.log('排序后的数据示例:', elementsWithData.slice(0, 3).map(item => ({
            id: item.productId,
            name: item.name,
            stock: item.stock,
            price: item.price
        })));

        // 重新排列DOM元素
        productList.innerHTML = '';
        elementsWithData.forEach(item => {
            productList.appendChild(item.element);
        });

    } catch (error) {
        console.error('排序失败:', error);
    } finally {
        // 恢复按钮状态
        sortBtn.textContent = originalText;
        sortBtn.disabled = false;
    }
}

// 更新库存样式
function updateStockStyle(stockElement, stockValue) {
    // 移除现有的样式类
    stockElement.classList.remove('stock-zero', 'stock-normal');

    // 根据库存值添加相应的样式类
    if (stockValue === 0) {
        stockElement.classList.add('stock-zero');
    } else {
        stockElement.classList.add('stock-normal');
    }
}


